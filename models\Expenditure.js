const mongoose = require('mongoose');

const expenditureSchema = new mongoose.Schema({
  expenseId: {
    type: String,
    required: true,
    unique: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Marketing', 'Operations', 'Inventory', 'Salaries', 'Rent', 'Utilities', 'Technology', 'Travel', 'Other']
  },
  subcategory: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  department: {
    type: String,
    required: true,
    enum: ['Sales', 'Marketing', 'Operations', 'HR', 'IT', 'Finance', 'General']
  },
  vendor: {
    type: String,
    required: true
  },
  approvedBy: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  month: {
    type: Number,
    required: true
  },
  year: {
    type: Number,
    required: true
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String
  }]
}, {
  timestamps: true
});

// Index for better query performance
expenditureSchema.index({ date: -1 });
expenditureSchema.index({ category: 1 });
expenditureSchema.index({ department: 1 });
expenditureSchema.index({ month: 1, year: 1 });

module.exports = mongoose.model('Expenditure', expenditureSchema);
