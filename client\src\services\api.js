import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Analytics API
export const analyticsAPI = {
  getOverview: (params = {}) => api.get('/analytics/overview', { params }),
  getMonthlyTrends: (params = {}) => api.get('/analytics/monthly-trends', { params }),
  getInventoryTurnover: (params = {}) => api.get('/analytics/inventory-turnover', { params }),
  getCategoryTrends: (params = {}) => api.get('/analytics/category-trends', { params }),
};

// Sales API
export const salesAPI = {
  getAll: (params = {}) => api.get('/sales', { params }),
  getMonthly: (params = {}) => api.get('/sales/monthly', { params }),
  getTopProducts: (params = {}) => api.get('/sales/top-products', { params }),
  getByRegion: () => api.get('/sales/by-region'),
  getByChannel: () => api.get('/sales/by-channel'),
  create: (data) => api.post('/sales', data),
};

// Expenditures API
export const expendituresAPI = {
  getAll: (params = {}) => api.get('/expenditures', { params }),
  getMonthly: (params = {}) => api.get('/expenditures/monthly', { params }),
  getByCategory: () => api.get('/expenditures/by-category'),
  getByDepartment: () => api.get('/expenditures/by-department'),
  create: (data) => api.post('/expenditures', data),
};

// Health check
export const healthCheck = () => api.get('/health');

export default api;
