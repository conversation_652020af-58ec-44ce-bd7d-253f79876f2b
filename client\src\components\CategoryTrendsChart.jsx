import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
  Bar
} from 'recharts';
import { TrendingUp, TrendingDown, Minus, Activity, AlertTriangle } from 'lucide-react';

const CategoryTrendsChart = ({ data = {} }) => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [viewMode, setViewMode] = useState('overview');

  const { categories = [], summary = {} } = data;

  const getTrendIcon = (direction, strength) => {
    const iconClass = "w-4 h-4";
    if (direction === 'uptrend') {
      return <TrendingUp className={`${iconClass} text-green-600`} />;
    } else if (direction === 'downtrend') {
      return <TrendingDown className={`${iconClass} text-red-600`} />;
    } else {
      return <Minus className={`${iconClass} text-gray-500`} />;
    }
  };

  const getTrendColor = (direction) => {
    if (direction === 'uptrend') return 'text-green-600 bg-green-50 border-green-200';
    if (direction === 'downtrend') return 'text-red-600 bg-red-50 border-red-200';
    return 'text-gray-600 bg-gray-50 border-gray-200';
  };

  const getMarketPositionIcon = (position) => {
    switch (position) {
      case 'growing':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'declining':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      case 'volatile':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      default:
        return <Activity className="w-4 h-4 text-blue-600" />;
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">Month {label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-800">Uptrending</p>
              <p className="text-2xl font-bold text-green-900">{summary.uptrending || 0}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-800">Downtrending</p>
              <p className="text-2xl font-bold text-red-900">{summary.downtrending || 0}</p>
            </div>
            <TrendingDown className="w-8 h-8 text-red-600" />
          </div>
        </div>
        
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-800">Stable</p>
              <p className="text-2xl font-bold text-gray-900">{summary.stable || 0}</p>
            </div>
            <Minus className="w-8 h-8 text-gray-600" />
          </div>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-800">Total Categories</p>
              <p className="text-2xl font-bold text-blue-900">{summary.totalCategories || 0}</p>
            </div>
            <Activity className="w-8 h-8 text-blue-600" />
          </div>
        </div>
      </div>

      {/* Category List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category, index) => (
          <div
            key={index}
            className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${getTrendColor(category.trend.direction)}`}
            onClick={() => {
              setSelectedCategory(category);
              setViewMode('detail');
            }}
          >
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-gray-900">{category.category}</h4>
              {getTrendIcon(category.trend.direction, category.trend.strength)}
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total Sales:</span>
                <span className="font-medium">{formatCurrency(category.totalSales)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Trend:</span>
                <span className={`font-medium ${category.trend.percentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {category.trend.percentage >= 0 ? '+' : ''}{category.trend.percentage}%
                </span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Market Position:</span>
                <div className="flex items-center space-x-1">
                  {getMarketPositionIcon(category.trend.marketPosition)}
                  <span className="font-medium capitalize">{category.trend.marketPosition}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderDetail = () => {
    if (!selectedCategory) return null;

    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const chartData = selectedCategory.monthlyData.map(month => ({
      ...month,
      monthName: monthNames[month.month - 1]
    }));

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900">{selectedCategory.category} - Detailed Analysis</h3>
            <p className="text-gray-600">Monthly performance and trend analysis</p>
          </div>
          <button
            onClick={() => setViewMode('overview')}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Back to Overview
          </button>
        </div>

        {/* Trend Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <p className="text-sm font-medium text-gray-600">Trend Direction</p>
            <div className="flex items-center space-x-2 mt-1">
              {getTrendIcon(selectedCategory.trend.direction)}
              <span className="font-bold capitalize">{selectedCategory.trend.direction}</span>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <p className="text-sm font-medium text-gray-600">Trend Strength</p>
            <p className="text-lg font-bold mt-1 capitalize">{selectedCategory.trend.strength}</p>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <p className="text-sm font-medium text-gray-600">Growth Rate</p>
            <p className={`text-lg font-bold mt-1 ${selectedCategory.trend.percentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {selectedCategory.trend.percentage >= 0 ? '+' : ''}{selectedCategory.trend.percentage}%
            </p>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <p className="text-sm font-medium text-gray-600">Volatility</p>
            <p className="text-lg font-bold mt-1">{selectedCategory.trend.volatility}%</p>
          </div>
        </div>

        {/* Monthly Trend Chart */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Monthly Sales Trend</h4>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="monthName" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} tickFormatter={formatCurrency} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="totalSales"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, stroke: '#3b82f6', strokeWidth: 2 }}
                  name="Sales"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-8">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-bold text-gray-900">Category Trend Analysis</h3>
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('overview')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'overview'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setViewMode('detail')}
              disabled={!selectedCategory}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'detail' && selectedCategory
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 disabled:opacity-50'
              }`}
            >
              Detail View
            </button>
          </div>
        </div>
        <p className="text-gray-600 text-sm">Analyze uptrend and downtrend patterns across product categories</p>
      </div>
      
      {viewMode === 'overview' ? renderOverview() : renderDetail()}
    </div>
  );
};

export default CategoryTrendsChart;
