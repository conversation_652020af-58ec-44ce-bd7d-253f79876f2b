import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  TrendingDown,
  PiggyBank,
  Percent,
  Users,
  ShoppingCart,
  BarChart3,
  RefreshCw,
  TrendingUp,
  Target,
  Award,
  Zap,
  Globe,
  Package
} from 'lucide-react';
import KPICard from './KPICard';
import MonthlyTrendsChart from './MonthlyTrendsChart';
import TopProductsChart from './TopProductsChart';
import SalesDistributionChart from './SalesDistributionChart';
import CategoryTrendsChart from './CategoryTrendsChart';
import { analyticsAPI, salesAPI } from '../services/api';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [overview, setOverview] = useState({});
  const [monthlyTrends, setMonthlyTrends] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [regionData, setRegionData] = useState([]);
  const [channelData, setChannelData] = useState([]);
  const [categoryTrends, setCategoryTrends] = useState({});
  const [error, setError] = useState(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching dashboard data from API...');

      const [
        overviewRes,
        trendsRes,
        productsRes,
        regionRes,
        channelRes,
        categoryTrendsRes
      ] = await Promise.all([
        analyticsAPI.getOverview(),
        analyticsAPI.getMonthlyTrends(),
        salesAPI.getTopProducts({ limit: 8 }),
        salesAPI.getByRegion(),
        salesAPI.getByChannel(),
        analyticsAPI.getCategoryTrends({ months: 6 })
      ]);

      console.log('✅ API Data received:', {
        overview: overviewRes.data,
        trends: trendsRes.data,
        products: productsRes.data,
        regions: regionRes.data,
        channels: channelRes.data,
        categoryTrends: categoryTrendsRes.data
      });

      setOverview(overviewRes.data);
      setMonthlyTrends(trendsRes.data);
      setTopProducts(productsRes.data);
      setRegionData(regionRes.data);
      setChannelData(channelRes.data);
      setCategoryTrends(categoryTrendsRes.data);

      console.log('✅ Dashboard data successfully loaded!');
    } catch (err) {
      console.error('❌ Error fetching dashboard data:', err);
      console.error('Error details:', err.response?.data || err.message);
      setError(`Failed to load dashboard data: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-primary-600" />
          <span className="text-lg text-gray-600">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg mb-4">{error}</div>
          <button
            onClick={fetchDashboardData}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header with Gradient */}
      <header className="glass shadow-xl border-b border-gray-200 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>

        <div className="max-w-7xl mx-auto px-6 lg:px-8 relative">
          <div className="flex justify-between items-center py-8">
            <div className="animate-fade-in">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-black gradient-text mb-2">
                Sales Dashboard
              </h1>
              <p className="text-lg text-gray-600 font-medium">
                Monitor your business performance and key metrics in real-time
              </p>
              <div className="flex items-center mt-3 space-x-4">
                <div className="flex items-center text-sm text-gray-500">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Live Data (2024)
                </div>
                <div className="text-sm text-gray-500">
                  Last updated: {new Date().toLocaleTimeString()}
                </div>
                <div className="text-sm text-gray-500">
                  500 Sales • 200 Expenses
                </div>
              </div>
            </div>
            <div className="animate-slide-up">
              <button
                onClick={fetchDashboardData}
                className="flex items-center space-x-3 px-6 py-3 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-all shadow-lg hover:shadow-xl font-semibold"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Refresh Data</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        {/* Primary KPI Section */}
        <section className="mb-12">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Key Performance Indicators</h2>
            <p className="text-gray-600">Your most important business metrics at a glance</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <KPICard
              title="Total Sales"
              value={overview.totalSales || 0}
              icon={DollarSign}
              format="currency"
            />
            <KPICard
              title="Total Expenditure"
              value={overview.totalExpenditure || 0}
              icon={TrendingDown}
              format="currency"
            />
            <KPICard
              title="Net Savings"
              value={overview.netSavings || 0}
              icon={PiggyBank}
              format="currency"
              changeType={overview.netSavings >= 0 ? 'positive' : 'negative'}
            />
            <KPICard
              title="Profit Margin"
              value={overview.profitMargin || 0}
              icon={Percent}
              format="percentage"
            />
          </div>
        </section>

        {/* Secondary KPI Section */}
        <section className="mb-12">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Business Insights</h2>
            <p className="text-gray-600">Detailed metrics for deeper business understanding</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <KPICard
              title="Total Transactions"
              value={overview.totalTransactions || 0}
              icon={ShoppingCart}
              format="number"
            />
            <KPICard
              title="Avg Transaction Value"
              value={overview.avgTransactionValue || 0}
              icon={BarChart3}
              format="currency"
            />
            <KPICard
              title="Unique Customers"
              value={overview.uniqueCustomers || 0}
              icon={Users}
              format="number"
            />
            <KPICard
              title="Customer Acquisition Cost"
              value={overview.customerAcquisitionCost || 0}
              icon={DollarSign}
              format="currency"
            />
          </div>
        </section>

        {/* Advanced Analytics Section */}
        <section className="mb-12">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Advanced Analytics</h2>
            <p className="text-gray-600">Performance indicators and growth metrics</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <KPICard
              title="Revenue Growth Rate"
              value={overview.revenueGrowthRate || 0}
              icon={TrendingUp}
              format="percentage"
              changeType={overview.revenueGrowthRate >= 0 ? 'positive' : 'negative'}
            />
            <KPICard
              title="Avg Customer Value"
              value={overview.avgCustomerValue || 0}
              icon={Target}
              format="currency"
            />
            <KPICard
              title="Operational Efficiency"
              value={overview.operationalEfficiency || 0}
              icon={Zap}
              format="number"
            />
            <KPICard
              title="Marketing ROI"
              value={overview.marketingROI || 0}
              icon={Award}
              format="percentage"
              changeType={overview.marketingROI >= 0 ? 'positive' : 'negative'}
            />
          </div>
        </section>

        {/* Performance Highlights Section */}
        <section className="mb-12">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Performance Highlights</h2>
            <p className="text-gray-600">Top performing segments and key metrics</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <KPICard
              title="Top Region"
              value={overview.topPerformingRegion || 'N/A'}
              icon={Globe}
              format="text"
            />
            <KPICard
              title="Top Channel"
              value={overview.topPerformingChannel || 'N/A'}
              icon={BarChart3}
              format="text"
            />
            <KPICard
              title="Total Quantity Sold"
              value={overview.totalQuantitySold || 0}
              icon={Package}
              format="number"
            />
            <KPICard
              title="Avg Items per Order"
              value={overview.avgQuantityPerTransaction || 0}
              icon={ShoppingCart}
              format="number"
            />
          </div>
        </section>

        {/* Analytics Charts Section */}
        <section className="mb-12">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Performance Analytics</h2>
            <p className="text-gray-600">Visual insights into your business trends and patterns</p>
          </div>

          {/* Monthly Trends - Full Width */}
          <div className="mb-8">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden card-hover animate-fade-in">
              <MonthlyTrendsChart data={monthlyTrends} />
            </div>
          </div>

          {/* Side by Side Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden card-hover animate-fade-in">
              <TopProductsChart data={topProducts} />
            </div>
            <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden card-hover animate-fade-in">
              <SalesDistributionChart
                regionData={regionData}
                channelData={channelData}
              />
            </div>
          </div>
        </section>

        {/* Category Trends Analysis Section */}
        <section className="mb-12">
          <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden card-hover animate-fade-in">
            <CategoryTrendsChart data={categoryTrends} />
          </div>
        </section>
      </main>
    </div>
  );
};

export default Dashboard;
