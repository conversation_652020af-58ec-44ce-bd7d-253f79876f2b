const express = require('express');
const router = express.Router();
const Expenditure = require('../models/Expenditure');

// Get all expenditures
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 50, startDate, endDate, category, department } = req.query;
    
    let query = {};
    
    // Date filter
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }
    
    // Category filter
    if (category) query.category = category;
    
    // Department filter
    if (department) query.department = department;
    
    const expenditures = await Expenditure.find(query)
      .sort({ date: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await Expenditure.countDocuments(query);
    
    res.json({
      expenditures,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get expenditures by month
router.get('/monthly', async (req, res) => {
  try {
    const { year = new Date().getFullYear() } = req.query;
    
    const monthlyExpenditures = await Expenditure.aggregate([
      {
        $match: { year: parseInt(year) }
      },
      {
        $group: {
          _id: '$month',
          totalExpenditure: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          avgExpenditure: { $avg: '$amount' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json(monthlyExpenditures);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get expenditures by category
router.get('/by-category', async (req, res) => {
  try {
    const expendituresByCategory = await Expenditure.aggregate([
      {
        $group: {
          _id: '$category',
          totalExpenditure: { $sum: '$amount' },
          totalTransactions: { $sum: 1 }
        }
      },
      {
        $sort: { totalExpenditure: -1 }
      }
    ]);
    
    res.json(expendituresByCategory);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get expenditures by department
router.get('/by-department', async (req, res) => {
  try {
    const expendituresByDepartment = await Expenditure.aggregate([
      {
        $group: {
          _id: '$department',
          totalExpenditure: { $sum: '$amount' },
          totalTransactions: { $sum: 1 }
        }
      },
      {
        $sort: { totalExpenditure: -1 }
      }
    ]);
    
    res.json(expendituresByDepartment);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create new expenditure
router.post('/', async (req, res) => {
  try {
    const expenditureData = req.body;
    const date = new Date(expenditureData.date || Date.now());
    
    const expenditure = new Expenditure({
      ...expenditureData,
      date,
      month: date.getMonth() + 1,
      year: date.getFullYear()
    });
    
    await expenditure.save();
    res.status(201).json(expenditure);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
