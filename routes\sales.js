const express = require('express');
const router = express.Router();
const Sale = require('../models/Sale');

// Get all sales
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 50, startDate, endDate, region, channel } = req.query;
    
    let query = {};
    
    // Date filter
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }
    
    // Region filter
    if (region) query.region = region;
    
    // Channel filter
    if (channel) query.channel = channel;
    
    const sales = await Sale.find(query)
      .sort({ date: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await Sale.countDocuments(query);
    
    res.json({
      sales,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get sales by month
router.get('/monthly', async (req, res) => {
  try {
    let { year = new Date().getFullYear() } = req.query;

    // Check if there's data for the requested year, if not, use 2024
    const currentYearData = await Sale.countDocuments({ year: parseInt(year) });
    if (currentYearData === 0) {
      year = 2024;
    }
    
    const monthlySales = await Sale.aggregate([
      {
        $match: { year: parseInt(year) }
      },
      {
        $group: {
          _id: '$month',
          totalSales: { $sum: '$totalAmount' },
          totalTransactions: { $sum: 1 },
          avgTransactionValue: { $avg: '$totalAmount' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json(monthlySales);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get top products by sales
router.get('/top-products', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const topProducts = await Sale.aggregate([
      {
        $group: {
          _id: {
            productId: '$productId',
            productName: '$productName'
          },
          totalSales: { $sum: '$totalAmount' },
          totalQuantity: { $sum: '$quantity' },
          totalTransactions: { $sum: 1 }
        }
      },
      {
        $sort: { totalSales: -1 }
      },
      {
        $limit: parseInt(limit)
      },
      {
        $project: {
          productId: '$_id.productId',
          productName: '$_id.productName',
          totalSales: 1,
          totalQuantity: 1,
          totalTransactions: 1,
          _id: 0
        }
      }
    ]);
    
    res.json(topProducts);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get sales by region
router.get('/by-region', async (req, res) => {
  try {
    const salesByRegion = await Sale.aggregate([
      {
        $group: {
          _id: '$region',
          totalSales: { $sum: '$totalAmount' },
          totalTransactions: { $sum: 1 }
        }
      },
      {
        $sort: { totalSales: -1 }
      }
    ]);
    
    res.json(salesByRegion);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get sales by channel
router.get('/by-channel', async (req, res) => {
  try {
    const salesByChannel = await Sale.aggregate([
      {
        $group: {
          _id: '$channel',
          totalSales: { $sum: '$totalAmount' },
          totalTransactions: { $sum: 1 }
        }
      },
      {
        $sort: { totalSales: -1 }
      }
    ]);
    
    res.json(salesByChannel);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create new sale
router.post('/', async (req, res) => {
  try {
    const saleData = req.body;
    const date = new Date(saleData.date || Date.now());
    
    const sale = new Sale({
      ...saleData,
      date,
      month: date.getMonth() + 1,
      year: date.getFullYear()
    });
    
    await sale.save();
    res.status(201).json(sale);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
