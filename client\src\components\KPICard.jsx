import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

const KPICard = ({ 
  title, 
  value, 
  change, 
  changeType = 'neutral', 
  icon: Icon, 
  format = 'currency',
  className = '' 
}) => {
  const formatValue = (val) => {
    if (format === 'currency') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(val);
    }
    if (format === 'percentage') {
      return `${val}%`;
    }
    if (format === 'number') {
      return new Intl.NumberFormat('en-US').format(val);
    }
    if (format === 'text') {
      return val;
    }
    return val;
  };

  const getTrendIcon = () => {
    if (changeType === 'positive') return <TrendingUp className="w-4 h-4" />;
    if (changeType === 'negative') return <TrendingDown className="w-4 h-4" />;
    return <Minus className="w-4 h-4" />;
  };

  const getTrendColor = () => {
    if (changeType === 'positive') return 'text-green-600';
    if (changeType === 'negative') return 'text-red-600';
    return 'text-gray-500';
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-200 p-8 card-hover animate-fade-in ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-semibold text-gray-500 mb-2 uppercase tracking-wider">{title}</p>
          <p className="text-3xl font-black text-gray-900 mb-3 transition-all">
            {formatValue(value)}
          </p>
          {change !== undefined && (
            <div className={`flex items-center text-sm font-medium ${getTrendColor()}`}>
              {getTrendIcon()}
              <span className="ml-2">
                {Math.abs(change)}% from last month
              </span>
            </div>
          )}
        </div>
        {Icon && (
          <div className="flex-shrink-0">
            <div className="w-16 h-16 bg-primary-50 rounded-2xl flex items-center justify-center shadow-lg transition-transform hover:scale-110">
              <Icon className="w-8 h-8 text-primary-600" />
            </div>
          </div>
        )}
      </div>

      {/* Decorative gradient line */}
      <div className="mt-6 h-1 bg-gradient-to-r from-primary-600 to-purple-600 rounded-full opacity-20"></div>
    </div>
  );
};

export default KPICard;
