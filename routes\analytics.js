const express = require('express');
const router = express.Router();
const Sale = require('../models/Sale');
const Expenditure = require('../models/Expenditure');
const Product = require('../models/Product');

// Get dashboard overview metrics
router.get('/overview', async (req, res) => {
  try {
    let { year = new Date().getFullYear(), month } = req.query;

    // Check if there's data for the requested year, if not, use 2024 (our sample data year)
    const currentYearData = await Sale.countDocuments({ year: parseInt(year) });
    if (currentYearData === 0) {
      year = 2024; // Default to 2024 where our sample data exists
      console.log('No data found for current year, defaulting to 2024');
    }

    let dateFilter = { year: parseInt(year) };
    if (month) {
      dateFilter.month = parseInt(month);
    }

    // Total Sales
    const totalSalesResult = await Sale.aggregate([
      { $match: dateFilter },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);
    const totalSales = totalSalesResult[0]?.total || 0;

    // Total Expenditure
    const totalExpenditureResult = await Expenditure.aggregate([
      { $match: dateFilter },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    const totalExpenditure = totalExpenditureResult[0]?.total || 0;
    
    // Net Savings
    const netSavings = totalSales - totalExpenditure;
    
    // Profit Margin
    const profitMargin = totalSales > 0 ? ((netSavings / totalSales) * 100) : 0;
    
    // Total Transactions
    const totalTransactions = await Sale.countDocuments(dateFilter);
    
    // Average Transaction Value
    const avgTransactionValue = totalTransactions > 0 ? totalSales / totalTransactions : 0;
    
    // Customer Acquisition Cost (CAC) - Marketing expenses / number of unique customers
    const marketingExpenses = await Expenditure.aggregate([
      { $match: { ...dateFilter, category: 'Marketing' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    const marketingCost = marketingExpenses[0]?.total || 0;
    
    const uniqueCustomers = await Sale.distinct('customerId', dateFilter);
    const cac = uniqueCustomers.length > 0 ? marketingCost / uniqueCustomers.length : 0;
    
    // Additional Business Insights

    // Revenue Growth Rate (comparing current period to previous)
    const previousYear = parseInt(year) - 1;
    const previousYearSales = await Sale.aggregate([
      { $match: { year: previousYear } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);
    const previousSales = previousYearSales[0]?.total || 0;
    const revenueGrowthRate = previousSales > 0 ? ((totalSales - previousSales) / previousSales * 100) : 0;

    // Top performing region
    const topRegion = await Sale.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$region', total: { $sum: '$totalAmount' } } },
      { $sort: { total: -1 } },
      { $limit: 1 }
    ]);

    // Top performing channel
    const topChannel = await Sale.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$channel', total: { $sum: '$totalAmount' } } },
      { $sort: { total: -1 } },
      { $limit: 1 }
    ]);

    // Average order value by channel
    const avgOrderByChannel = await Sale.aggregate([
      { $match: dateFilter },
      { $group: {
        _id: '$channel',
        avgOrder: { $avg: '$totalAmount' },
        totalOrders: { $sum: 1 }
      } },
      { $sort: { avgOrder: -1 } }
    ]);

    // Expense breakdown by category
    const expenseBreakdown = await Expenditure.aggregate([
      { $match: dateFilter },
      { $group: {
        _id: '$category',
        total: { $sum: '$amount' },
        percentage: { $sum: '$amount' }
      } },
      { $sort: { total: -1 } }
    ]);

    // Calculate percentages for expense breakdown
    expenseBreakdown.forEach(item => {
      item.percentage = totalExpenditure > 0 ? Math.round((item.total / totalExpenditure) * 100 * 100) / 100 : 0;
    });

    // Sales performance metrics
    const salesMetrics = await Sale.aggregate([
      { $match: dateFilter },
      { $group: {
        _id: null,
        totalQuantity: { $sum: '$quantity' },
        avgQuantityPerTransaction: { $avg: '$quantity' },
        maxTransactionValue: { $max: '$totalAmount' },
        minTransactionValue: { $min: '$totalAmount' }
      }}
    ]);

    const salesStats = salesMetrics[0] || {};

    res.json({
      // Core KPIs
      totalSales: Math.round(totalSales * 100) / 100,
      totalExpenditure: Math.round(totalExpenditure * 100) / 100,
      netSavings: Math.round(netSavings * 100) / 100,
      profitMargin: Math.round(profitMargin * 100) / 100,

      // Transaction Metrics
      totalTransactions,
      avgTransactionValue: Math.round(avgTransactionValue * 100) / 100,
      maxTransactionValue: Math.round((salesStats.maxTransactionValue || 0) * 100) / 100,
      minTransactionValue: Math.round((salesStats.minTransactionValue || 0) * 100) / 100,

      // Customer Metrics
      uniqueCustomers: uniqueCustomers.length,
      customerAcquisitionCost: Math.round(cac * 100) / 100,
      avgCustomerValue: uniqueCustomers.length > 0 ? Math.round((totalSales / uniqueCustomers.length) * 100) / 100 : 0,

      // Growth & Performance
      revenueGrowthRate: Math.round(revenueGrowthRate * 100) / 100,
      topPerformingRegion: topRegion[0]?._id || 'N/A',
      topPerformingChannel: topChannel[0]?._id || 'N/A',

      // Product Metrics
      totalQuantitySold: salesStats.totalQuantity || 0,
      avgQuantityPerTransaction: Math.round((salesStats.avgQuantityPerTransaction || 0) * 100) / 100,

      // Additional Insights
      expenseBreakdown,
      avgOrderByChannel,

      // Operational Metrics
      operationalEfficiency: totalExpenditure > 0 ? Math.round((totalSales / totalExpenditure) * 100) / 100 : 0,
      marketingROI: marketingCost > 0 ? Math.round(((totalSales - marketingCost) / marketingCost) * 100 * 100) / 100 : 0
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get monthly trends (sales vs expenditures)
router.get('/monthly-trends', async (req, res) => {
  try {
    let { year = new Date().getFullYear() } = req.query;

    // Check if there's data for the requested year, if not, use 2024
    const currentYearData = await Sale.countDocuments({ year: parseInt(year) });
    if (currentYearData === 0) {
      year = 2024;
    }
    
    const salesTrends = await Sale.aggregate([
      { $match: { year: parseInt(year) } },
      {
        $group: {
          _id: '$month',
          sales: { $sum: '$totalAmount' },
          transactions: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    const expenditureTrends = await Expenditure.aggregate([
      { $match: { year: parseInt(year) } },
      {
        $group: {
          _id: '$month',
          expenditure: { $sum: '$amount' }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    // Merge sales and expenditure data
    const monthlyTrends = [];
    for (let month = 1; month <= 12; month++) {
      const salesData = salesTrends.find(s => s._id === month);
      const expenditureData = expenditureTrends.find(e => e._id === month);
      
      monthlyTrends.push({
        month,
        monthName: new Date(year, month - 1).toLocaleString('default', { month: 'short' }),
        sales: salesData?.sales || 0,
        expenditure: expenditureData?.expenditure || 0,
        netSavings: (salesData?.sales || 0) - (expenditureData?.expenditure || 0),
        transactions: salesData?.transactions || 0
      });
    }
    
    res.json(monthlyTrends);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get inventory turnover
router.get('/inventory-turnover', async (req, res) => {
  try {
    const { year = new Date().getFullYear() } = req.query;
    
    const inventoryTurnover = await Sale.aggregate([
      { $match: { year: parseInt(year) } },
      {
        $group: {
          _id: '$productId',
          productName: { $first: '$productName' },
          totalQuantitySold: { $sum: '$quantity' },
          totalSales: { $sum: '$totalAmount' }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'productId',
          as: 'product'
        }
      },
      {
        $unwind: {
          path: '$product',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          productId: '$_id',
          productName: 1,
          totalQuantitySold: 1,
          totalSales: 1,
          currentStock: '$product.currentStock',
          turnoverRatio: {
            $cond: {
              if: { $gt: ['$product.currentStock', 0] },
              then: { $divide: ['$totalQuantitySold', '$product.currentStock'] },
              else: 0
            }
          }
        }
      },
      { $sort: { turnoverRatio: -1 } }
    ]);
    
    res.json(inventoryTurnover);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get category trends analysis
router.get('/category-trends', async (req, res) => {
  try {
    let { year = new Date().getFullYear(), months = 6 } = req.query;

    // Check if there's data for the requested year, if not, use 2024
    const currentYearData = await Sale.countDocuments({ year: parseInt(year) });
    if (currentYearData === 0) {
      year = 2024;
    }

    const monthsToAnalyze = parseInt(months);

    // Get category performance by month for trend analysis
    const categoryTrends = await Sale.aggregate([
      {
        $match: {
          year: parseInt(year),
          month: { $gte: Math.max(1, 13 - monthsToAnalyze), $lte: 12 }
        }
      },
      {
        $group: {
          _id: {
            category: '$category',
            month: '$month'
          },
          totalSales: { $sum: '$totalAmount' },
          totalQuantity: { $sum: '$quantity' },
          transactionCount: { $sum: 1 },
          avgPrice: { $avg: '$unitPrice' }
        }
      },
      {
        $group: {
          _id: '$_id.category',
          monthlyData: {
            $push: {
              month: '$_id.month',
              totalSales: '$totalSales',
              totalQuantity: '$totalQuantity',
              transactionCount: '$transactionCount',
              avgPrice: '$avgPrice'
            }
          },
          totalSales: { $sum: '$totalSales' },
          totalQuantity: { $sum: '$totalQuantity' }
        }
      },
      { $sort: { totalSales: -1 } }
    ]);

    // Calculate trends for each category
    const categoryTrendsWithAnalysis = categoryTrends.map(category => {
      const monthlyData = category.monthlyData.sort((a, b) => a.month - b.month);

      // Calculate trend direction
      let trendDirection = 'stable';
      let trendPercentage = 0;
      let trendStrength = 'weak';

      if (monthlyData.length >= 2) {
        const firstHalf = monthlyData.slice(0, Math.ceil(monthlyData.length / 2));
        const secondHalf = monthlyData.slice(Math.floor(monthlyData.length / 2));

        const firstHalfAvg = firstHalf.reduce((sum, month) => sum + month.totalSales, 0) / firstHalf.length;
        const secondHalfAvg = secondHalf.reduce((sum, month) => sum + month.totalSales, 0) / secondHalf.length;

        trendPercentage = firstHalfAvg > 0 ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100 : 0;

        if (Math.abs(trendPercentage) > 20) {
          trendStrength = 'strong';
        } else if (Math.abs(trendPercentage) > 10) {
          trendStrength = 'moderate';
        }

        if (trendPercentage > 5) {
          trendDirection = 'uptrend';
        } else if (trendPercentage < -5) {
          trendDirection = 'downtrend';
        }
      }

      // Calculate volatility (standard deviation of monthly sales)
      const salesValues = monthlyData.map(m => m.totalSales);
      const avgSales = salesValues.reduce((sum, val) => sum + val, 0) / salesValues.length;
      const variance = salesValues.reduce((sum, val) => sum + Math.pow(val - avgSales, 2), 0) / salesValues.length;
      const volatility = Math.sqrt(variance);
      const volatilityPercentage = avgSales > 0 ? (volatility / avgSales) * 100 : 0;

      // Determine market position
      let marketPosition = 'stable';
      if (trendDirection === 'uptrend' && volatilityPercentage < 30) {
        marketPosition = 'growing';
      } else if (trendDirection === 'downtrend' && volatilityPercentage < 30) {
        marketPosition = 'declining';
      } else if (volatilityPercentage > 50) {
        marketPosition = 'volatile';
      }

      return {
        category: category._id,
        totalSales: Math.round(category.totalSales * 100) / 100,
        totalQuantity: category.totalQuantity,
        monthlyData: monthlyData.map(m => ({
          ...m,
          totalSales: Math.round(m.totalSales * 100) / 100,
          avgPrice: Math.round(m.avgPrice * 100) / 100
        })),
        trend: {
          direction: trendDirection,
          percentage: Math.round(trendPercentage * 100) / 100,
          strength: trendStrength,
          volatility: Math.round(volatilityPercentage * 100) / 100,
          marketPosition
        }
      };
    });

    // Separate categories by trend
    const uptrending = categoryTrendsWithAnalysis.filter(c => c.trend.direction === 'uptrend');
    const downtrending = categoryTrendsWithAnalysis.filter(c => c.trend.direction === 'downtrend');
    const stable = categoryTrendsWithAnalysis.filter(c => c.trend.direction === 'stable');

    res.json({
      categories: categoryTrendsWithAnalysis,
      summary: {
        totalCategories: categoryTrendsWithAnalysis.length,
        uptrending: uptrending.length,
        downtrending: downtrending.length,
        stable: stable.length,
        topGrowing: uptrending.slice(0, 3),
        topDeclining: downtrending.slice(0, 3)
      },
      analysisParams: {
        year: parseInt(year),
        monthsAnalyzed: monthsToAnalyze
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
