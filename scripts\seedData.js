const mongoose = require('mongoose');
const Sale = require('../models/Sale');
const Expenditure = require('../models/Expenditure');
const Product = require('../models/Product');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sales-dashboard';

// Sample data
const products = [
  { productId: 'P001', name: 'Laptop Pro 15"', category: 'Electronics', brand: 'TechCorp', costPrice: 800, sellingPrice: 1200, currentStock: 50, supplier: 'TechSupply Inc' },
  { productId: 'P002', name: 'Wireless Mouse', category: 'Electronics', brand: 'TechCorp', costPrice: 15, sellingPrice: 25, currentStock: 200, supplier: 'TechSupply Inc' },
  { productId: 'P003', name: 'Office Chair', category: 'Furniture', brand: 'ComfortSeats', costPrice: 120, sellingPrice: 180, currentStock: 30, supplier: 'Furniture Plus' },
  { productId: 'P004', name: 'Smartphone X', category: 'Electronics', brand: 'MobileTech', costPrice: 400, sellingPrice: 600, currentStock: 75, supplier: 'Mobile Distributors' },
  { productId: 'P005', name: 'Desk Lamp', category: 'Furniture', brand: 'BrightLight', costPrice: 30, sellingPrice: 50, currentStock: 100, supplier: 'Lighting Solutions' },
  { productId: 'P006', name: 'Coffee Maker', category: 'Appliances', brand: 'BrewMaster', costPrice: 80, sellingPrice: 120, currentStock: 40, supplier: 'Appliance World' },
  { productId: 'P007', name: 'Bluetooth Speaker', category: 'Electronics', brand: 'SoundWave', costPrice: 50, sellingPrice: 80, currentStock: 60, supplier: 'Audio Tech' },
  { productId: 'P008', name: 'Standing Desk', category: 'Furniture', brand: 'ErgoWork', costPrice: 200, sellingPrice: 300, currentStock: 25, supplier: 'Furniture Plus' }
];

const regions = ['North', 'South', 'East', 'West', 'Central'];
const channels = ['Online', 'Retail', 'Wholesale', 'Direct'];
const salesPersons = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Emily Brown', 'David Wilson'];
const customers = [
  'ABC Corp', 'XYZ Ltd', 'Tech Solutions Inc', 'Global Enterprises', 'Innovation Hub',
  'Smart Systems', 'Future Tech', 'Digital Dynamics', 'NextGen Solutions', 'Prime Industries'
];

const expenditureCategories = ['Marketing', 'Operations', 'Inventory', 'Salaries', 'Rent', 'Utilities', 'Technology'];
const departments = ['Sales', 'Marketing', 'Operations', 'HR', 'IT', 'Finance'];
const vendors = ['Office Supplies Co', 'Marketing Agency', 'Tech Services', 'Utility Company', 'Real Estate Group'];

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function generateSales() {
  const sales = [];
  const startDate = new Date('2024-01-01');
  const endDate = new Date('2024-12-31');
  
  for (let i = 1; i <= 500; i++) {
    const product = getRandomElement(products);
    const date = getRandomDate(startDate, endDate);
    const quantity = Math.floor(Math.random() * 10) + 1;
    const totalAmount = quantity * product.sellingPrice;
    
    sales.push({
      transactionId: `TXN${String(i).padStart(6, '0')}`,
      productId: product.productId,
      productName: product.name,
      category: product.category,
      quantity,
      unitPrice: product.sellingPrice,
      totalAmount,
      customerId: `CUST${String(Math.floor(Math.random() * 100) + 1).padStart(3, '0')}`,
      customerName: getRandomElement(customers),
      region: getRandomElement(regions),
      channel: getRandomElement(channels),
      salesPerson: getRandomElement(salesPersons),
      date,
      month: date.getMonth() + 1,
      year: date.getFullYear()
    });
  }
  
  return sales;
}

function generateExpenditures() {
  const expenditures = [];
  const startDate = new Date('2024-01-01');
  const endDate = new Date('2024-12-31');
  
  for (let i = 1; i <= 200; i++) {
    const date = getRandomDate(startDate, endDate);
    const category = getRandomElement(expenditureCategories);
    const amount = Math.floor(Math.random() * 5000) + 100;
    
    expenditures.push({
      expenseId: `EXP${String(i).padStart(6, '0')}`,
      category,
      subcategory: `${category} - Subcategory`,
      description: `${category} expense for business operations`,
      amount,
      department: getRandomElement(departments),
      vendor: getRandomElement(vendors),
      approvedBy: getRandomElement(salesPersons),
      date,
      month: date.getMonth() + 1,
      year: date.getFullYear(),
      isRecurring: Math.random() > 0.7
    });
  }
  
  return expenditures;
}

async function seedDatabase() {
  try {
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('Connected to MongoDB');
    
    // Clear existing data
    await Sale.deleteMany({});
    await Expenditure.deleteMany({});
    await Product.deleteMany({});
    
    console.log('Cleared existing data');
    
    // Insert products
    await Product.insertMany(products);
    console.log('Inserted products');
    
    // Insert sales
    const sales = generateSales();
    await Sale.insertMany(sales);
    console.log('Inserted sales data');
    
    // Insert expenditures
    const expenditures = generateExpenditures();
    await Expenditure.insertMany(expenditures);
    console.log('Inserted expenditure data');
    
    console.log('Database seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
