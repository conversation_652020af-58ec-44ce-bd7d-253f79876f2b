import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

const TopProductsChart = ({ data = [] }) => {
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          <p className="text-sm text-blue-600">
            Sales: {formatCurrency(data.totalSales)}
          </p>
          <p className="text-sm text-gray-600">
            Quantity: {data.totalQuantity.toLocaleString()} units
          </p>
          <p className="text-sm text-gray-600">
            Transactions: {data.totalTransactions.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  // Truncate long product names for display
  const processedData = data.map(item => ({
    ...item,
    displayName: item.productName.length > 15 
      ? item.productName.substring(0, 15) + '...' 
      : item.productName
  }));

  return (
    <div className="p-8">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Top Products by Sales</h3>
        <p className="text-gray-600 text-sm">Best performing products by revenue</p>
      </div>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={processedData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis 
              dataKey="displayName" 
              stroke="#64748b"
              fontSize={12}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              stroke="#64748b"
              fontSize={12}
              tickFormatter={formatCurrency}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="totalSales" 
              fill="#3b82f6"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default TopProductsChart;
