const mongoose = require('mongoose');

const saleSchema = new mongoose.Schema({
  transactionId: {
    type: String,
    required: true,
    unique: true
  },
  productId: {
    type: String,
    required: true
  },
  productName: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  customerId: {
    type: String,
    required: true
  },
  customerName: {
    type: String,
    required: true
  },
  region: {
    type: String,
    required: true,
    enum: ['North', 'South', 'East', 'West', 'Central']
  },
  channel: {
    type: String,
    required: true,
    enum: ['Online', 'Retail', 'Wholesale', 'Direct']
  },
  salesPerson: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  month: {
    type: Number,
    required: true
  },
  year: {
    type: Number,
    required: true
  }
}, {
  timestamps: true
});

// Index for better query performance
saleSchema.index({ date: -1 });
saleSchema.index({ productId: 1 });
saleSchema.index({ region: 1 });
saleSchema.index({ channel: 1 });
saleSchema.index({ month: 1, year: 1 });

module.exports = mongoose.model('Sale', saleSchema);
